import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Utility untuk setup user dan troubleshooting
class UserSetup {
  static final _supabase = Supabase.instance.client;

  /// Buat akun user baru di Supabase Auth dan database
  static Future<Map<String, dynamic>> createUserAccount({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      // 1. Buat user di Supabase Auth
      final authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName},
      );

      if (authResponse.user == null) {
        return {
          'success': false,
          'message': 'Gagal membuat akun di authentication',
        };
      }

      final user = authResponse.user!;

      // 2. Buat data user di tabel users
      await _supabase.from('users').insert({
        'id': user.id,
        'nama_lengkap': fullName,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // 3. Buat pengaturan suhu default
      await _supabase.from('temperature_settings').insert({
        'user_id': user.id,
        'min_temperature': 25.0,
        'max_temperature': 30.0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'message': 'Akun berhasil dibuat',
        'user_id': user.id,
        'email': email,
      };
    } catch (e) {
      debugPrint('Error creating user account: $e');
      return {
        'success': false,
        'message': 'Error: ${e.toString()}',
      };
    }
  }

  /// Cek dan perbaiki data user yang hilang
  static Future<Map<String, dynamic>> fixUserData(String userId) async {
    try {
      // Cek apakah user ada di tabel users
      final userResponse = await _supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

      if (userResponse == null) {
        // User tidak ada di tabel users, buat data baru
        final authUser = _supabase.auth.currentUser;
        if (authUser == null) {
          return {
            'success': false,
            'message': 'User tidak login',
          };
        }

        await _supabase.from('users').insert({
          'id': userId,
          'nama_lengkap': authUser.userMetadata?['full_name'] ?? 
                         authUser.email ?? 'User',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      // Cek apakah ada pengaturan suhu
      final tempSettingResponse = await _supabase
          .from('temperature_settings')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      if (tempSettingResponse == null) {
        // Buat pengaturan suhu default
        await _supabase.from('temperature_settings').insert({
          'user_id': userId,
          'min_temperature': 25.0,
          'max_temperature': 30.0,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      return {
        'success': true,
        'message': 'Data user berhasil diperbaiki',
      };
    } catch (e) {
      debugPrint('Error fixing user data: $e');
      return {
        'success': false,
        'message': 'Error: ${e.toString()}',
      };
    }
  }

  /// Cek status lengkap user
  static Future<Map<String, dynamic>> checkUserStatus(String userId) async {
    try {
      final results = <String, dynamic>{
        'user_id': userId,
        'auth_user_exists': false,
        'database_user_exists': false,
        'temperature_settings_exists': false,
        'issues': <String>[],
      };

      // Cek auth user
      final authUser = _supabase.auth.currentUser;
      results['auth_user_exists'] = authUser != null && authUser.id == userId;
      
      if (!results['auth_user_exists']) {
        results['issues'].add('User tidak ditemukan di authentication');
      }

      // Cek database user
      final userResponse = await _supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .maybeSingle();
      
      results['database_user_exists'] = userResponse != null;
      if (!results['database_user_exists']) {
        results['issues'].add('User tidak ditemukan di database');
      }

      // Cek temperature settings
      final tempResponse = await _supabase
          .from('temperature_settings')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();
      
      results['temperature_settings_exists'] = tempResponse != null;
      if (!results['temperature_settings_exists']) {
        results['issues'].add('Pengaturan suhu tidak ditemukan');
      }

      results['success'] = (results['issues'] as List).isEmpty;
      
      return results;
    } catch (e) {
      debugPrint('Error checking user status: $e');
      return {
        'success': false,
        'message': 'Error: ${e.toString()}',
      };
    }
  }

  /// Buat beberapa akun demo untuk testing
  static Future<List<Map<String, dynamic>>> createDemoAccounts() async {
    final accounts = [
      {
        'email': '<EMAIL>',
        'password': 'admin123',
        'fullName': 'Administrator',
      },
      {
        'email': '<EMAIL>',
        'password': 'user123',
        'fullName': 'User Biasa',
      },
    ];

    final results = <Map<String, dynamic>>[];

    for (final account in accounts) {
      final result = await createUserAccount(
        email: account['email']!,
        password: account['password']!,
        fullName: account['fullName']!,
      );
      results.add({
        'email': account['email'],
        'result': result,
      });
    }

    return results;
  }
}
