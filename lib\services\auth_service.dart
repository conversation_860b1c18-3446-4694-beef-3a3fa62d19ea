import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user.dart' as app_models;
import '../models/temperature_setting.dart';
import 'database_service.dart';

class AuthService {
  static final _supabase = Supabase.instance.client;
  static final DatabaseService _databaseService = DatabaseService();

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Stream untuk mendengarkan perubahan auth state
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Getter untuk current user
  User? get currentUser => _supabase.auth.currentUser;
  String? get currentUserId => _supabase.auth.currentUser?.id;
  bool get isLoggedIn => _supabase.auth.currentUser != null;

  /// Sign in dengan email dan password
  Future<AuthResult> signInWithEmailPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Cek atau buat data user di database
        await _ensureUserDataExists(response.user!);

        return AuthResult(
          success: true,
          user: response.user,
          message: 'Login berhasil',
        );
      } else {
        return AuthResult(
          success: false,
          message: 'Login gagal',
        );
      }
    } on AuthException catch (e) {
      return AuthResult(
        success: false,
        message: _getAuthErrorMessage(e),
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'Terjadi kesalahan: ${e.toString()}',
      );
    }
  }

  /// Sign out
  Future<bool> signOut() async {
    try {
      await _supabase.auth.signOut();
      return true;
    } catch (e) {
      debugPrint('Error signing out: $e');
      return false;
    }
  }

  /// Reset password
  Future<AuthResult> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
      return AuthResult(
        success: true,
        message: 'Link reset password telah dikirim ke email Anda',
      );
    } on AuthException catch (e) {
      return AuthResult(
        success: false,
        message: _getAuthErrorMessage(e),
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'Terjadi kesalahan: ${e.toString()}',
      );
    }
  }

  /// Pastikan data user ada di database
  Future<void> _ensureUserDataExists(User user) async {
    try {
      final existingUser = await _databaseService.getUserById(user.id);

      if (existingUser == null) {
        debugPrint(
            'User data not found in database, creating new user data...');
        // Buat data user baru
        await _createUserData(
            user, user.userMetadata?['full_name'] ?? user.email ?? 'User');
        debugPrint('User data created successfully');
      } else {
        debugPrint('User data found in database');
      }
    } catch (e) {
      debugPrint('Error ensuring user data exists: $e');
      // Jangan throw error, biarkan user tetap bisa login
      // Error akan ditangani di level aplikasi
    }
  }

  /// Buat data user di database
  Future<void> _createUserData(User user, String fullName) async {
    try {
      final appUser = app_models.User(
        id: user.id,
        namaLengkap: fullName,
        profileImageUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.upsertUser(appUser);

      // Buat pengaturan suhu default
      try {
        await _databaseService.upsertTemperatureSetting(
          TemperatureSetting(
            pengaturanId: 'default_${user.id}',
            userId: user.id,
            minTemperature: 25.0,
            maxTemperature: 30.0,
            updatedAt: DateTime.now(),
          ),
        );
        debugPrint('Default temperature settings created');
      } catch (e) {
        debugPrint('Error creating default temperature settings: $e');
      }
    } catch (e) {
      debugPrint('Error creating user data: $e');
    }
  }

  /// Convert auth error ke pesan yang user-friendly
  String _getAuthErrorMessage(AuthException error) {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'Email atau password salah';
      case 'Email not confirmed':
        return 'Email belum diverifikasi. Silakan cek email Anda';
      case 'User already registered':
        return 'Email sudah terdaftar';
      case 'Password should be at least 6 characters':
        return 'Password minimal 6 karakter';
      case 'Unable to validate email address: invalid format':
        return 'Format email tidak valid';
      case 'Signup requires a valid password':
        return 'Password tidak valid';
      default:
        return error.message;
    }
  }

  /// Cek apakah user sudah login saat app dimulai
  Future<bool> checkAuthStatus() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user != null) {
        // Pastikan data user ada di database
        await _ensureUserDataExists(user);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error checking auth status: $e');
      return false;
    }
  }
}

/// Class untuk hasil authentication
class AuthResult {
  final bool success;
  final User? user;
  final String message;

  AuthResult({
    required this.success,
    this.user,
    required this.message,
  });
}
