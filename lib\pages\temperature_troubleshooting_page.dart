import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/sensor_service.dart';
import '../models/temperature_setting.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class TemperatureTroubleshootingPage extends StatefulWidget {
  const TemperatureTroubleshootingPage({super.key});

  @override
  State<TemperatureTroubleshootingPage> createState() =>
      _TemperatureTroubleshootingPageState();
}

class _TemperatureTroubleshootingPageState
    extends State<TemperatureTroubleshootingPage> {
  final _databaseService = DatabaseService();
  final _sensorService = SensorService();
  final _supabase = Supabase.instance.client;

  bool _isLoading = false;
  String _statusMessage = '';
  TemperatureSetting? _currentSetting;

  @override
  void initState() {
    super.initState();
    _checkTemperatureSettings();
  }

  Future<void> _checkTemperatureSettings() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Mengecek pengaturan suhu...';
    });

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        setState(() {
          _statusMessage = 'Error: User tidak login';
        });
        return;
      }

      // Cek pengaturan suhu di database
      final setting = await _databaseService.getTemperatureSetting(userId);

      String message = 'Status Pengaturan Suhu:\n\n';
      message += 'User ID: $userId\n';

      if (setting != null) {
        _currentSetting = setting;
        message += '✅ Pengaturan suhu ditemukan\n';
        message += 'Min: ${setting.minTemperature}°C\n';
        message += 'Max: ${setting.maxTemperature}°C\n';
        message += 'ID: ${setting.pengaturanId}\n';
        message += 'Updated: ${setting.updatedAt}\n\n';

        // Cek apakah sensor service memiliki pengaturan yang sama
        final sensorSetting = _sensorService.currentTemperatureSetting;
        if (sensorSetting != null) {
          message += '✅ Sensor service memiliki pengaturan\n';
          if (sensorSetting.pengaturanId == setting.pengaturanId) {
            message += '✅ Pengaturan sinkron\n';
          } else {
            message += '⚠️ Pengaturan tidak sinkron\n';
          }
        } else {
          message += '❌ Sensor service tidak memiliki pengaturan\n';
        }
      } else {
        message += '❌ Pengaturan suhu tidak ditemukan\n';
        message += 'Perlu membuat pengaturan default\n';
      }

      setState(() {
        _statusMessage = message;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultSettings() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Membuat pengaturan default...';
    });

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User tidak login');
      }

      final setting = TemperatureSetting(
        pengaturanId: 'setting_$userId',
        userId: userId,
        minTemperature: 25.0,
        maxTemperature: 30.0,
        updatedAt: DateTime.now(),
      );

      final savedSetting =
          await _databaseService.upsertTemperatureSetting(setting);

      if (savedSetting != null) {
        await _sensorService.updateTemperatureSetting(savedSetting);
        setState(() {
          _statusMessage =
              'Pengaturan default berhasil dibuat:\nMin: 25.0°C\nMax: 30.0°C';
          _currentSetting = savedSetting;
        });
      } else {
        throw Exception('Gagal menyimpan pengaturan');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncSettings() async {
    if (_currentSetting == null) {
      setState(() {
        _statusMessage = 'Tidak ada pengaturan untuk disinkronkan';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Menyinkronkan pengaturan...';
    });

    try {
      await _sensorService.updateTemperatureSetting(_currentSetting!);
      setState(() {
        _statusMessage =
            'Pengaturan berhasil disinkronkan dengan sensor service';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error sinkronisasi: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _viewRawData() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Mengambil data mentah...';
    });

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User tidak login');
      }

      final response = await _supabase
          .from('temperature_settings')
          .select('*')
          .eq('user_id', userId)
          .order('updated_at', ascending: false);

      String message = 'Data Mentah dari Database:\n\n';
      if (response.isEmpty) {
        message += 'Tidak ada data ditemukan';
      } else {
        message += 'Total records: ${response.length}\n';
        if (response.length > 1) {
          message += '⚠️ DUPLIKASI DITEMUKAN!\n\n';
        }

        for (var i = 0; i < response.length; i++) {
          final data = response[i];
          message += 'Record ${i + 1}${i == 0 ? ' (TERBARU)' : ''}:\n';
          message += 'pengaturan_id: ${data['pengaturan_id']}\n';
          message += 'user_id: ${data['user_id']}\n';
          message += 'min_temperature: ${data['min_temperature']}\n';
          message += 'max_temperature: ${data['max_temperature']}\n';
          message += 'created_at: ${data['created_at']}\n';
          message += 'updated_at: ${data['updated_at']}\n\n';
        }
      }

      setState(() {
        _statusMessage = message;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _cleanupDuplicates() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Membersihkan duplikasi...';
    });

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User tidak login');
      }

      // Get all records for this user
      final response = await _supabase
          .from('temperature_settings')
          .select('*')
          .eq('user_id', userId)
          .order('updated_at', ascending: false);

      if (response.length <= 1) {
        setState(() {
          _statusMessage = 'Tidak ada duplikasi yang ditemukan';
        });
        return;
      }

      // Keep the latest record, delete the rest
      final latest = response.first;
      final toDelete = response.skip(1).toList();

      for (final record in toDelete) {
        await _supabase
            .from('temperature_settings')
            .delete()
            .eq('pengaturan_id', record['pengaturan_id']);
      }

      setState(() {
        _statusMessage = 'Berhasil menghapus ${toDelete.length} duplikasi.\n'
            'Record yang dipertahankan:\n'
            'Min: ${latest['min_temperature']}°C\n'
            'Max: ${latest['max_temperature']}°C\n'
            'Updated: ${latest['updated_at']}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Troubleshooting Pengaturan Suhu'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Action Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Aksi Troubleshooting',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed:
                                _isLoading ? null : _checkTemperatureSettings,
                            child: const Text('Cek Status'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed:
                                _isLoading ? null : _createDefaultSettings,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Buat Default'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _syncSettings,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Sinkronkan'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _viewRawData,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Data Mentah'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _cleanupDuplicates,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Bersihkan Duplikasi'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Status Message
            if (_statusMessage.isNotEmpty)
              Card(
                color: Colors.grey[100],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Status',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _statusMessage,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),

            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),
              ),

            const SizedBox(height: 16),

            // Help Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Bantuan',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Cek Status: Memeriksa pengaturan suhu saat ini\n'
                      '• Buat Default: Membuat pengaturan suhu default (25-30°C)\n'
                      '• Sinkronkan: Menyinkronkan pengaturan dengan sensor service\n'
                      '• Data Mentah: Melihat data mentah dari database\n'
                      '• Bersihkan Duplikasi: Menghapus data duplikat',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
