# Alur Aplikasi AquaTemp IoT - Bahasa Indonesia

## 🚀 1. ALUR MEMULAI APLIKASI

```mermaid
graph TD
    A[Buka Aplikasi] --> B[main.dart]
    B --> C[Inisialisasi Supabase]
    C --> D[Jalankan MyApp]
    D --> E[AuthWrapper]
    E --> F{Cek Status Login}
    F -->|Sudah Login| G[Halaman Utama]
    F -->|Belum Login| H[Halaman Login]

    G --> I[Inisialisasi Layanan]
    I --> J[Layanan Database]
    I --> K[Layanan Sensor]
    I --> L[Layanan Pemanas]
    I --> M[Layanan ESP32]
    I --> N[Layanan Kontrol Otomatis]
```

### Urutan Memulai Aplikasi:
1. **main.dart** - Titik masuk aplikasi
2. **Supabase.initialize()** - Setup koneksi database
3. **AuthWrapper** - Cek status login pengguna
4. **Inisialisasi Layanan** - <PERSON><PERSON><PERSON> semua layanan
5. **<PERSON><PERSON>lan UI** - Menampilkan halaman sesuai status login

---

## 🔐 2. ALUR MASUK (LOGIN)

```mermaid
graph TD
    A[Halaman Login] --> B[Input Email/Password]
    B --> C[AuthService.masukDenganEmailPassword]
    C --> D{Autentikasi}
    D -->|Berhasil| E[Buat/Update Data Pengguna]
    E --> F[Pindah ke Halaman Utama]
    D -->|Gagal| G[Tampilkan Pesan Error]
    G --> A

    F --> H[Inisialisasi Layanan Pengguna]
    H --> I[Muat Pengaturan Pengguna]
    H --> J[Mulai Monitoring Real-time]
```

### Langkah-langkah Login:
1. **Input Login** - Pengguna masukkan email/password
2. **Validasi Supabase** - Cek kredensial di database
3. **Sinkronisasi Data** - Sinkronkan data pengguna
4. **Setup Layanan** - Inisialisasi layanan untuk pengguna
5. **Mulai Monitoring** - Memulai monitoring real-time

---

## 🏠 3. ALUR APLIKASI UTAMA

```mermaid
graph TD
    A[Halaman Utama] --> B[Navigasi Bawah]
    B --> C[Beranda]
    B --> D[Pengaturan Suhu]
    B --> E[Riwayat]
    B --> F[Profil]

    C --> G[Monitoring Real-time]
    C --> H[Tampilan Suhu]
    C --> I[Kontrol Pemanas]
    C --> J[Grafik Statistik]

    D --> K[Pengaturan Suhu]
    D --> L[Konfigurasi Min/Max]

    E --> M[Data Riwayat]
    E --> N[Statistik Penggunaan]

    F --> O[Profil Pengguna]
    F --> P[Fungsi Logout]
    F --> Q[Akses Kontrol ESP32]
```

### Navigasi Utama:
- **Beranda** - Dashboard utama dengan monitoring
- **Pengaturan Suhu** - Atur batas suhu minimum dan maksimum
- **Riwayat** - Lihat riwayat penggunaan pemanas
- **Profil** - Profil pengguna dan pengaturan

---

## 📊 4. ALUR MONITORING REAL-TIME

```mermaid
graph TD
    A[Layanan Sensor] --> B[ESP32Service.bacaDataSensor]
    B --> C[HTTP Request ke ESP32]
    C --> D[Pembacaan Sensor DS18B20]
    D --> E[Kembalikan Data Suhu]
    E --> F[Simpan ke Database]
    F --> G[Update Stream UI]
    G --> H[Tampilan Beranda]
    G --> I[Cek Kontrol Otomatis]
```

### Proses Monitoring:
1. **Pengumpulan Data** - Dari ESP32 hardware
2. **Penyimpanan Database** - Simpan ke tabel sensor_data
3. **Update Stream** - Update UI secara real-time
4. **Kontrol Otomatis** - Trigger kontrol otomatis

---

## 🌡️ 5. ALUR KONTROL SUHU

```mermaid
graph TD
    A[Data Suhu Baru] --> B[LayananKontrolOtomatis]
    B --> C{Mode Otomatis Aktif?}
    C -->|Tidak| D[Hanya Kontrol Manual]
    C -->|Ya| E[Cek Status Suhu]

    E --> F{Suhu vs Pengaturan}
    F -->|< Suhu Min| G[Nyalakan Pemanas]
    F -->|> Suhu Max| H[Nyalakan Pompa]
    F -->|Suhu Normal| I[Matikan Keduanya]

    G --> J[ESP32Service.kontrolRelay]
    H --> J
    I --> J

    J --> K[Kirim Perintah HTTP]
    K --> L[Kontrol Relay ESP32]
    L --> M[Update Status Database]
    M --> N[Catat Riwayat]
    N --> O[Update UI]
```

### Logika Kontrol:
1. **Analisis Suhu** - Bandingkan dengan pengaturan
2. **Pengambilan Keputusan** - Tentukan aksi yang diperlukan
3. **Kontrol Perangkat** - Kirim perintah ke ESP32
4. **Update Status** - Update database dan UI
5. **Pencatatan Riwayat** - Catat semua aktivitas

---

## 🔌 6. ALUR KOMUNIKASI ESP32

```mermaid
graph TD
    A[Aplikasi Flutter] --> B[LayananESP32]
    B --> C{Jenis Perintah}
    C -->|Baca Sensor| D[GET /api/sensor]
    C -->|Kontrol Relay| E[POST /api/command]
    C -->|Cek Status| F[GET /api/ping]

    D --> G[Web Server ESP32]
    E --> G
    F --> G

    G --> H[Pembacaan DS18B20]
    G --> I[Kontrol Relay]
    G --> J[Respon Status]

    H --> K[Kembalikan Data JSON]
    I --> K
    J --> K

    K --> L[Handler Respon Flutter]
    L --> M[Update State Lokal]
    M --> N[Update Database]
    N --> O[Update UI]
```

### Langkah Komunikasi:
1. **Pembuatan Perintah** - Flutter buat HTTP request
2. **Transfer Jaringan** - Kirim ke ESP32 via WiFi
3. **Pemrosesan ESP32** - Proses perintah di ESP32
4. **Aksi Hardware** - Kontrol sensor/relay
5. **Respon Balik** - Kirim respon kembali
6. **Update State** - Update state di Flutter

---

## 📱 7. ALUR ANTARMUKA PENGGUNA

```mermaid
graph TD
    A[Interaksi Pengguna] --> B{Jenis Aksi}
    B -->|Lihat Data| C[Langganan Stream]
    B -->|Ubah Pengaturan| D[Update Database]
    B -->|Kontrol Manual| E[Kirim Perintah]

    C --> F[Update Real-time]
    F --> G[Rebuild UI]

    D --> H[Validasi Input]
    H --> I[Simpan ke Database]
    I --> J[Update Layanan]
    J --> G

    E --> K[Cek Izin]
    K --> L[Eksekusi Perintah]
    L --> M[Tampilkan Feedback]
    M --> G
```

### Jenis Interaksi UI:
- **Melihat Pasif** - Tampilan data real-time
- **Ubah Pengaturan** - Update konfigurasi
- **Kontrol Aktif** - Kontrol perangkat manual
- **Navigasi** - Pindah antar halaman

---

## 🗄️ 8. ALUR OPERASI DATABASE

```mermaid
graph TD
    A[Operasi Database] --> B{Jenis Operasi}
    B -->|Baca| C[Query SELECT]
    B -->|Tulis| D[Query INSERT]
    B -->|Update| E[Query UPDATE]
    B -->|Stream| F[Langganan Real-time]

    C --> G[Klien Supabase]
    D --> G
    E --> G
    F --> G

    G --> H[Database PostgreSQL]
    H --> I[Kembalikan Hasil]
    I --> J[Konversi Model]
    J --> K[Layer Layanan]
    K --> L[Update UI]
```

### Tabel Database:
- **users** - Data profil pengguna
- **sensor_data** - Pembacaan suhu
- **temperature_settings** - Preferensi pengguna
- **heater_status** - Status pemanas saat ini
- **heater_history** - Riwayat penggunaan
- **esp32_devices** - Informasi perangkat
- **relay_controls** - Konfigurasi relay

---

## ⚙️ 9. ARSITEKTUR LAYER LAYANAN

```mermaid
graph TD
    A[Layer UI] --> B[Layer Layanan]
    B --> C[LayananAuth]
    B --> D[LayananDatabase]
    B --> E[LayananSensor]
    B --> F[LayananPemanas]
    B --> G[LayananESP32]
    B --> H[LayananKontrolOtomatis]

    C --> I[Supabase Auth]
    D --> J[Database Supabase]
    E --> K[Stream Real-time]
    F --> L[Kontrol Perangkat]
    G --> M[Komunikasi HTTP]
    H --> N[Logika Otomasi]

    I --> O[Layanan Eksternal]
    J --> O
    K --> P[Sumber Data]
    L --> P
    M --> Q[Hardware ESP32]
    N --> R[Logika Kontrol]
```

### Tanggung Jawab Layanan:
- **LayananAuth** - Manajemen autentikasi
- **LayananDatabase** - Operasi database
- **LayananSensor** - Penanganan data sensor
- **LayananPemanas** - Logika kontrol pemanas
- **LayananESP32** - Komunikasi hardware
- **LayananKontrolOtomatis** - Logika otomasi

---

## 🔄 10. ALUR SISTEM LENGKAP

```mermaid
graph TD
    A[Hardware ESP32] --> B[Sensor DS18B20]
    B --> C[Pembacaan Suhu]
    C --> D[Web Server ESP32]
    D --> E[HTTP API]

    E --> F[Aplikasi Flutter]
    F --> G[LayananESP32]
    G --> H[LayananSensor]
    H --> I[LayananKontrolOtomatis]

    I --> J{Logika Kontrol Otomatis}
    J -->|Perlu Panas| K[Perintah Pemanas ON]
    J -->|Perlu Dingin| L[Perintah Pompa ON]
    J -->|Suhu Normal| M[Perintah Semua OFF]

    K --> N[ESP32Service.kontrolRelay]
    L --> N
    M --> N

    N --> O[Perintah HTTP ke ESP32]
    O --> P[Kontrol Relay]
    P --> Q[Aksi Pemanas/Pompa]

    Q --> R[Perubahan Suhu]
    R --> B

    F --> S[LayananDatabase]
    S --> T[Database Supabase]
    T --> U[Penyimpanan Data]

    F --> V[Update UI]
    V --> W[Antarmuka Pengguna]
    W --> X[Interaksi Pengguna]
    X --> F
```

---

## 📋 11. ALUR PENANGANAN ERROR

```mermaid
graph TD
    A[Mulai Operasi] --> B{Coba Operasi}
    B -->|Berhasil| C[Kembalikan Hasil]
    B -->|Error| D[Tangkap Exception]

    D --> E{Jenis Error}
    E -->|Error Jaringan| F[Logika Retry]
    E -->|Error Auth| G[Redirect ke Login]
    E -->|Error Validasi| H[Tampilkan Pesan User]
    E -->|Error Sistem| I[Log Error]

    F --> J{Jumlah Retry}
    J -->|< Max Retry| B
    J -->|Max Tercapai| K[Gagal dengan Baik]

    G --> L[Hapus Sesi]
    H --> M[Feedback Pengguna]
    I --> N[Debug Logging]
    K --> M

    L --> O[Halaman Login]
    M --> P[Lanjutkan Operasi]
    N --> P
```

### Jenis Error:
- **Error Jaringan** - Masalah koneksi
- **Error Autentikasi** - Masalah login
- **Error Validasi** - Validasi input
- **Error Hardware** - Komunikasi ESP32
- **Error Database** - Masalah Supabase

---

## 🎯 12. ALUR LENGKAP STARTUP KE OPERASIONAL

```
1. Buka Aplikasi
   ↓
2. Inisialisasi Supabase
   ↓
3. Cek Autentikasi
   ↓
4. Login (jika diperlukan)
   ↓
5. Inisialisasi Layanan
   ↓
6. Koneksi ESP32
   ↓
7. Mulai Monitoring Real-time
   ↓
8. Aktifkan Kontrol Otomatis
   ↓
9. Antarmuka Pengguna Siap
   ↓
10. Operasi Berkelanjutan
```

### Status Operasional:
- **Startup** - Inisialisasi komponen
- **Autentikasi** - Proses login pengguna
- **Koneksi** - Menghubungkan ke ESP32
- **Monitoring** - Pengumpulan data real-time
- **Kontrol** - Kontrol suhu otomatis
- **Siap** - Status operasional penuh

---

## 📊 13. RINGKASAN ALUR DATA

```
Hardware → ESP32 → HTTP API → Aplikasi Flutter → Database → UI
    ↑                                                        ↓
    └─────────── Perintah Kontrol ←─────────────────────────┘
```

### Alur Data Utama:
1. **Data Sensor**: ESP32 → Flutter → Database → UI
2. **Perintah Kontrol**: UI → Flutter → ESP32 → Hardware
3. **Pengaturan Pengguna**: UI → Database → Layanan → ESP32
4. **Data Riwayat**: Database → Layanan → UI
5. **Update Real-time**: Database → Stream → UI

Alur aplikasi ini menunjukkan bagaimana semua komponen bekerja sama untuk menciptakan sistem IoT monitoring suhu air yang lengkap dan otomatis dalam Bahasa Indonesia! 🌊📱🔧
