class SensorData {
  final String suhuAir;
  final String humidity;
  final String waterLevel;
  final DateTime tanggalWaktu;
  final String userId;
  final String dataId;
  final DateTime? createdAt;

  SensorData({
    required this.suhuAir,
    required this.humidity,
    required this.waterLevel,
    required this.tanggalWaktu,
    required this.userId,
    required this.dataId,
    this.createdAt,
  });

  factory SensorData.fromJson(Map<String, dynamic> json) {
    return SensorData(
      suhuAir: json['suhu_air']?.toString() ?? '0.0',
      humidity: json['humidity']?.toString() ?? '0.0',
      waterLevel: json['water_level']?.toString() ?? '0.0',
      tanggalWaktu: json['tanggal_waktu'] != null
          ? DateTime.parse(json['tanggal_waktu'] as String)
          : DateTime.now(),
      userId: json['user_id']?.toString() ?? '',
      dataId: json['data_id']?.toString() ??
          'unknown_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'suhu_air': suhuAir,
      'humidity': humidity,
      'water_level': waterLevel,
      'tanggal_waktu': tanggalWaktu.toIso8601String(),
      'user_id': userId,
      'data_id': dataId,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Getter untuk mendapatkan nilai suhu sebagai double
  double get suhuAirValue {
    try {
      return double.parse(suhuAir);
    } catch (e) {
      return 0.0;
    }
  }

  // Getter untuk mendapatkan nilai humidity sebagai double
  double get humidityValue {
    try {
      return double.parse(humidity);
    } catch (e) {
      return 0.0;
    }
  }

  // Getter untuk mendapatkan nilai water level sebagai double
  double get waterLevelValue {
    try {
      return double.parse(waterLevel);
    } catch (e) {
      return 0.0;
    }
  }

  SensorData copyWith({
    String? suhuAir,
    String? humidity,
    String? waterLevel,
    DateTime? tanggalWaktu,
    String? userId,
    String? dataId,
    DateTime? createdAt,
  }) {
    return SensorData(
      suhuAir: suhuAir ?? this.suhuAir,
      humidity: humidity ?? this.humidity,
      waterLevel: waterLevel ?? this.waterLevel,
      tanggalWaktu: tanggalWaktu ?? this.tanggalWaktu,
      userId: userId ?? this.userId,
      dataId: dataId ?? this.dataId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'SensorData(dataId: $dataId, suhuAir: $suhuAir°C, humidity: $humidity%, waterLevel: $waterLevel, tanggalWaktu: $tanggalWaktu)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SensorData && other.dataId == dataId;
  }

  @override
  int get hashCode => dataId.hashCode;
}
