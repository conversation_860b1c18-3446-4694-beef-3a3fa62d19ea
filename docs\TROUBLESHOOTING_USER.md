# Troubleshooting: Data Pengguna Tidak Ditemukan

## Ma<PERSON>ah <PERSON>

### 1. "Data pengguna tidak ditemukan"
**Penyebab:**
- User ada di Supabase Auth tapi tidak ada di tabel `users`
- Pengaturan suhu default tidak dibuat
- Koneksi database bermasalah

**Solusi:**

#### A. Menggunakan User Setup Page
1. Buka aplikasi dan klik "Setup & Troubleshooting User" di halaman login
2. <PERSON><PERSON> "Cek Status" untuk melihat masalah yang ada
3. K<PERSON> "Perbaiki Data" untuk memperbaiki data user yang hilang

#### B. Manual di Supabase Dashboard
1. Buka Supabase Dashboard → Authentication → Users
2. Pastikan user ada di daftar
3. Buka Database → Table Editor → `users`
4. Cek apakah ada record dengan `id` yang sama dengan user auth
5. Jika tidak ada, tambahkan manual:
```sql
INSERT INTO users (id, nama_lengkap, created_at, updated_at)
VALUES ('user-id-dari-auth', '<PERSON>a User', NOW(), NOW());
```

#### C. Buat Pengaturan Suhu Default
```sql
INSERT INTO temperature_settings (user_id, min_temperature, max_temperature, created_at, updated_at)
VALUES ('user-id-dari-auth', 25.0, 30.0, NOW(), NOW());
```

### 2. User Tidak Bisa Login
**Penyebab:**
- Email/password salah
- User tidak ada di Supabase Auth
- Koneksi internet bermasalah

**Solusi:**
1. Pastikan email dan password benar
2. Cek koneksi internet
3. Buat akun baru melalui User Setup Page

### 3. Data Sensor Tidak Muncul
**Penyebab:**
- User ID tidak valid
- Tidak ada data sensor untuk user tersebut
- Service tidak terinisialisasi

**Solusi:**
1. Cek status user melalui User Setup Page
2. Pastikan ESP32 terhubung dan mengirim data
3. Restart aplikasi

## Cara Membuat Akun Baru

### 1. Melalui User Setup Page
1. Buka aplikasi
2. Klik "Setup & Troubleshooting User" di halaman login
3. Isi form "Buat Akun Baru":
   - Email: <EMAIL>
   - Password: minimal 6 karakter
   - Nama Lengkap: nama user
4. Klik "Buat Akun"

### 2. Akun Demo untuk Testing
1. Klik "Buat Akun Demo" di User Setup Page
2. Akan membuat 2 akun:
   - <EMAIL> (password: admin123)
   - <EMAIL> (password: user123)

### 3. Manual di Supabase Dashboard
1. Buka Supabase Dashboard → Authentication → Users
2. Klik "Add User"
3. Masukkan email dan password
4. Klik "Create User"
5. Login ke aplikasi dengan akun tersebut
6. Sistem akan otomatis membuat data user di database

## Struktur Database yang Diperlukan

### Tabel `users`
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  nama_lengkap TEXT NOT NULL,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Tabel `temperature_settings`
```sql
CREATE TABLE temperature_settings (
  pengaturan_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  min_temperature DECIMAL(5,2) NOT NULL DEFAULT 25.0,
  max_temperature DECIMAL(5,2) NOT NULL DEFAULT 30.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Debugging Steps

### 1. Cek Console Log
Buka Developer Tools dan lihat console untuk error messages:
- "User data not found in database" - User perlu dibuat di database
- "Error ensuring user data exists" - Masalah koneksi database
- "Error creating user data" - Masalah permission atau struktur database

### 2. Cek Supabase Dashboard
1. **Authentication → Users**: Pastikan user ada
2. **Database → users**: Pastikan record user ada
3. **Database → temperature_settings**: Pastikan pengaturan ada
4. **Logs**: Cek error logs untuk masalah database

### 3. Test Koneksi
1. Buka User Setup Page
2. Klik "Cek Status" untuk user yang sedang login
3. Lihat hasil diagnosis

## Prevention

### 1. Automatic User Creation
Sistem sudah diatur untuk otomatis membuat data user saat login pertama kali. Jika masih ada masalah:
1. Cek permission di Supabase RLS (Row Level Security)
2. Pastikan tabel `users` dan `temperature_settings` ada
3. Cek koneksi internet

### 2. Regular Backup
1. Export data user secara berkala
2. Backup database schema
3. Monitor logs untuk error patterns

## Contact Support
Jika masalah masih berlanjut:
1. Screenshot error message
2. Catat langkah-langkah yang dilakukan
3. Hubungi administrator sistem
