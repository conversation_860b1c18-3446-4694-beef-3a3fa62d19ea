import 'package:flutter/material.dart';
import '../utils/user_setup.dart';
import '../services/auth_service.dart';

class UserSetupPage extends StatefulWidget {
  const UserSetupPage({super.key});

  @override
  State<UserSetupPage> createState() => _UserSetupPageState();
}

class _UserSetupPageState extends State<UserSetupPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  final AuthService _authService = AuthService();
  
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _createAccount() async {
    if (_emailController.text.isEmpty || 
        _passwordController.text.isEmpty || 
        _nameController.text.isEmpty) {
      setState(() {
        _statusMessage = 'Semua field harus diisi';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Membuat akun...';
    });

    final result = await UserSetup.createUserAccount(
      email: _emailController.text.trim(),
      password: _passwordController.text,
      fullName: _nameController.text.trim(),
    );

    setState(() {
      _isLoading = false;
      _statusMessage = result['message'];
    });

    if (result['success']) {
      _emailController.clear();
      _passwordController.clear();
      _nameController.clear();
    }
  }

  Future<void> _createDemoAccounts() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Membuat akun demo...';
    });

    final results = await UserSetup.createDemoAccounts();
    
    String message = 'Hasil pembuatan akun demo:\n';
    for (final result in results) {
      final email = result['email'];
      final success = result['result']['success'];
      message += '• $email: ${success ? 'Berhasil' : 'Gagal'}\n';
    }

    setState(() {
      _isLoading = false;
      _statusMessage = message;
    });
  }

  Future<void> _fixCurrentUser() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      setState(() {
        _statusMessage = 'Tidak ada user yang login';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Memperbaiki data user...';
    });

    final result = await UserSetup.fixUserData(currentUser.id);

    setState(() {
      _isLoading = false;
      _statusMessage = result['message'];
    });
  }

  Future<void> _checkCurrentUser() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      setState(() {
        _statusMessage = 'Tidak ada user yang login';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Mengecek status user...';
    });

    final result = await UserSetup.checkUserStatus(currentUser.id);

    String message = 'Status User ${currentUser.email}:\n';
    message += '• Auth User: ${result['auth_user_exists'] ? 'OK' : 'TIDAK ADA'}\n';
    message += '• Database User: ${result['database_user_exists'] ? 'OK' : 'TIDAK ADA'}\n';
    message += '• Temperature Settings: ${result['temperature_settings_exists'] ? 'OK' : 'TIDAK ADA'}\n';
    
    if (result['issues'] != null && (result['issues'] as List).isNotEmpty) {
      message += '\nMasalah ditemukan:\n';
      for (final issue in result['issues']) {
        message += '• $issue\n';
      }
    }

    setState(() {
      _isLoading = false;
      _statusMessage = message;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup & Troubleshooting User'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current User Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'User Saat Ini',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _authService.currentUser?.email ?? 'Tidak ada user login',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _checkCurrentUser,
                            child: const Text('Cek Status'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _fixCurrentUser,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Perbaiki Data'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Create New Account
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Buat Akun Baru',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _passwordController,
                      obscureText: true,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Nama Lengkap',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _createAccount,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Buat Akun'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Demo Accounts
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Akun Demo',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Buat akun demo untuk testing:\n'
                      '• <EMAIL> (admin123)\n'
                      '• <EMAIL> (user123)',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _createDemoAccounts,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Buat Akun Demo'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Status Message
            if (_statusMessage.isNotEmpty)
              Card(
                color: Colors.grey[100],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Status',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _statusMessage,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),

            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
