import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/database_service.dart';
import '../services/sensor_service.dart';
import '../models/temperature_setting.dart';
import 'temperature_troubleshooting_page.dart';

class UpdateSuhuPage extends StatelessWidget {
  const UpdateSuhuPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF2F5FC),
      appBar: AppBar(
        title: const Text('Atur Batas Suhu'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TemperatureTroubleshootingPage(),
                ),
              );
            },
            icon: const Icon(Icons.help_outline),
            tooltip: 'Troubleshooting',
          ),
        ],
      ),
      body: const SafeArea(
        child: Column(
          children: [
            Expanded(child: UpdateSuhuContent()),
          ],
        ),
      ),
    );
  }
}

class UpdateSuhuContent extends StatefulWidget {
  const UpdateSuhuContent({super.key});

  @override
  State<UpdateSuhuContent> createState() => _UpdateSuhuContentState();
}

class _UpdateSuhuContentState extends State<UpdateSuhuContent> {
  final _minController = TextEditingController();
  final _maxController = TextEditingController();
  bool _isLoading = false;
  final _supabase = Supabase.instance.client;
  final _databaseService = DatabaseService();
  final _sensorService = SensorService();

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  Future<void> _loadCurrentSettings() async {
    try {
      setState(() => _isLoading = true);

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User tidak login');
      }

      final setting = await _databaseService.getTemperatureSetting(userId);

      if (mounted) {
        setState(() {
          if (setting != null) {
            _minController.text = setting.minTemperature.toString();
            _maxController.text = setting.maxTemperature.toString();
          } else {
            // Set default values jika tidak ada pengaturan
            _minController.text = '25.0';
            _maxController.text = '30.0';
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading temperature settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal memuat pengaturan suhu: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        // Set default values on error
        setState(() {
          _minController.text = '25.0';
          _maxController.text = '30.0';
        });
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveSettings() async {
    final min = double.tryParse(_minController.text);
    final max = double.tryParse(_maxController.text);

    if (min == null || max == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Mohon masukkan angka yang valid'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (min >= max) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Suhu minimum harus lebih kecil dari suhu maksimum'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      setState(() => _isLoading = true);

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User tidak login');
      }

      // Buat atau update pengaturan suhu menggunakan model dan service
      final setting = TemperatureSetting(
        pengaturanId: 'setting_$userId', // ID unik untuk user
        userId: userId,
        minTemperature: min,
        maxTemperature: max,
        updatedAt: DateTime.now(),
      );

      // Simpan menggunakan database service (akan melakukan UPSERT)
      final savedSetting =
          await _databaseService.upsertTemperatureSetting(setting);

      if (savedSetting != null) {
        // Update sensor service dengan pengaturan baru
        await _sensorService.updateTemperatureSetting(savedSetting);

        if (mounted) {
          // Show success dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Sukses'),
              content: const Text('Pengaturan suhu berhasil disimpan'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context); // Go back to previous page
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
      } else {
        throw Exception('Gagal menyimpan pengaturan');
      }
    } catch (e) {
      debugPrint('Error saving temperature settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal menyimpan pengaturan suhu: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      const Text("Batas Suhu Minimum"),
                      const SizedBox(height: 10),
                      TextFormField(
                        controller: _minController,
                        decoration: InputDecoration(
                          hintText: "Contoh: 24",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 20),
                      const Text("Batas Suhu Maksimum"),
                      const SizedBox(height: 10),
                      TextFormField(
                        controller: _maxController,
                        decoration: InputDecoration(
                          hintText: "Contoh: 32",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _saveSettings,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            backgroundColor: Colors.blue,
                          ),
                          child: const Text(
                            "Simpan",
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  @override
  void dispose() {
    _minController.dispose();
    _maxController.dispose();
    super.dispose();
  }
}
